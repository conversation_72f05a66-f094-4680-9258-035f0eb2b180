{"name": "back-end-acara", "version": "1.0.0", "type": "commonjs", "description": "", "main": "index.js", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "dev": "nodemon --exec ts-node src/index.ts", "docs": "ts-node src/docs/swagger.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/mongoose": "^5.11.97", "@types/nodemailer": "^6.4.17", "@types/swagger-ui-express": "^4.1.8", "body-parser": "^1.20.3", "cors": "^2.8.5", "dotenv": "^16.4.7", "ejs": "^3.1.10", "express": "^4.19.2", "jsonwebtoken": "^9.0.2", "mongoose": "^8.9.2", "nodemailer": "^6.9.14", "nodemon": "^3.1.9", "swagger-autogen": "^2.23.7", "swagger-ui-express": "4.6.3", "ts-node": "^10.9.2", "typescript": "^5.4.5", "yup": "^1.6.1"}, "devDependencies": {"@types/ejs": "^3.1.5", "@types/jsonwebtoken": "^9.0.9"}}