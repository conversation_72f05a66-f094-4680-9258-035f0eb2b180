{"openapi": "3.0.0", "info": {"version": "v0.0.1", "title": "Dokumentasi", "description": "Dokumentasi"}, "servers": [{"url": " http://localhost:3000/api/", "description": "Local Server"}, {"url": " https://back-end-acara-beta-lilac.vercel.app/api/", "description": "Deploy Server"}], "paths": {"/auth/register": {"post": {"tags": ["<PERSON><PERSON>"], "description": "", "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}, "requestBody": {"requires": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/RegisterRequest"}}}}}}, "/auth/login": {"post": {"tags": ["<PERSON><PERSON>"], "description": "", "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}}, "requestBody": {"requires": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/LoginRequest"}}}}}}, "/auth/me": {"get": {"tags": ["<PERSON><PERSON>"], "description": "", "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}, "403": {"description": "Forbidden"}}, "security": [{"bearerAuth": []}]}}, "/auth/activation": {"post": {"tags": ["<PERSON><PERSON>"], "description": "", "responses": {"200": {"description": "OK"}, "400": {"description": "Bad Request"}}, "requestBody": {"requires": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ActivaionRequset"}}, "application/xml": {"schema": {"$ref": "#/components/schemas/ActivaionRequset"}}}}}}}, "components": {"securitySchemes": {"bearerAuth": {"type": "http", "scheme": "bearer"}}, "schemas": {"LoginRequest": {"type": "object", "properties": {"identifier": {"type": "string", "example": "vayne"}, "password": {"type": "string", "example": "123456"}}, "xml": {"name": "LoginRequest"}}, "RegisterRequest": {"type": "object", "properties": {"fullName": {"type": "string", "example": "vayne"}, "userName": {"type": "string", "example": "vayne"}, "email": {"type": "string", "example": "<EMAIL>"}, "password": {"type": "string", "example": "123456"}, "confirmPassword": {"type": "string", "example": "123456"}}, "xml": {"name": "RegisterRequest"}}, "ActivaionRequset": {"type": "object", "properties": {"code": {"type": "string", "example": "abcdef"}}, "xml": {"name": "ActivaionRequset"}}}}}